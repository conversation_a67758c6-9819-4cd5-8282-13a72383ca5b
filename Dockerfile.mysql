FROM mysql:8.0

# Install cron for scheduled backups
RUN apt-get update && apt-get install -y cron && rm -rf /var/lib/apt/lists/*

# Copy initialization scripts
COPY ./db/ /docker-entrypoint-initdb.d/

# Copy backup and restore scripts
COPY ./backup.sh /backup.sh
COPY ./restore.sh /restore.sh

# Copy custom entrypoint script
COPY ./docker-entrypoint.sh /docker-entrypoint.sh

# Make scripts executable
RUN chmod +x /backup.sh /restore.sh /docker-entrypoint.sh

# Set custom entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["mysqld"]

# Expose MySQL port (optional)
EXPOSE 3306