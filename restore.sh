#!/bin/bash

# Define backup directory
BACKUP_DIR=/backups

# Find the latest backup file
LATEST_BACKUP=$(ls -t ${BACKUP_DIR}/backup_*.sql 2>/dev/null | head -n 1)

# Check if backup file exists
if [ -z "$LATEST_BACKUP" ]; then
    echo "$(date): No backup files found in ${BACKUP_DIR}" >> /var/log/backup.log
    exit 1
fi

# Restore the database
mysql -ucompany -p$MYSQL_PASSWORD company_db < $LATEST_BACKUP
echo "$(date): Database restored from ${LATEST_BACKUP}" >> /var/log/backup.log

# Clean up old backups (older than 30 days)
find ${BACKUP_DIR} -type f -mtime +30 -name 'backup_*.sql' -delete