#!/bin/bash
set -eo pipefail

echo "Starting custom MySQL entrypoint..."

# Start cron service in background
service cron start

# Set up backup cron job (every 5 minutes)
echo "*/5 * * * * /backup.sh" | crontab -
echo "Backup cron job scheduled (every 5 minutes)"

# Create backup directory if it doesn't exist
mkdir -p /backups

# Create log directory
mkdir -p /var/log
touch /var/log/backup.log

# Check if we should restore from backup on startup
if [ -f "/backups/backup_*.sql" ] && [ ! -d "/var/lib/mysql/$MYSQL_DATABASE" ]; then
    echo "Backup files found, will restore after MySQL initialization..."
fi

# Run the original MySQL entrypoint
exec docker-entrypoint.sh "$@"