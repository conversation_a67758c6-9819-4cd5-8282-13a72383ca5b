#!/bin/bash

# Define backup directory (mapped to host via docker-compose)
BACKUP_DIR=/backups
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
BACKUP_FILE="${BACKUP_DIR}/backup_${TIMESTAMP}.sql"
LOG_FILE="/var/log/backup.log"

# Create backup directory if it doesn’t exist
mkdir -p ${BACKUP_DIR}

# Create log file if it doesn't exist
touch ${LOG_FILE}

# Check if MySQL is ready
if ! mysqladmin ping -ucompany -p$MYSQL_PASSWORD --silent; then
    echo "$(date): MySQL is not ready, skipping backup" >> ${LOG_FILE}
    exit 1
fi

# Dump the database to a timestamped file
if mysqldump -ucompany -p$MYSQL_PASSWORD company_db > ${BACKUP_FILE} 2>>${LOG_FILE}; then
    # Set permissions so the file is readable
    chmod 644 ${BACKUP_FILE}

    # Log successful backup
    echo "$(date): Database backup saved to ${BACKUP_FILE}" >> ${LOG_FILE}

    # Clean up old backups (keep only last 10 backups)
    ls -t ${BACKUP_DIR}/backup_*.sql | tail -n +11 | xargs -r rm

    echo "$(date): Old backups cleaned up" >> ${LOG_FILE}
else
    echo "$(date): Backup failed for ${BACKUP_FILE}" >> ${LOG_FILE}
    # Remove failed backup file if it exists
    [ -f "${BACKUP_FILE}" ] && rm "${BACKUP_FILE}"
    exit 1
fi